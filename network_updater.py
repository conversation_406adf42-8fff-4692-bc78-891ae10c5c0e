#!/usr/bin/env python3
"""
Simple CSV Network Updater
Updates network column in CSV by calling the link builder API to find the "Used" merchant.
"""

import csv
import requests
import time
import json
from urllib.parse import urlparse

PHIA_INTERNAL_API_ENDPOINT = "https://api.phia.com/internal/graphql"
X_PHIA_TOKEN = "KVhqb0FjSyNxW3NbK2xweQ=="

CSV_FILE_PATH = "/Users/<USER>/Documents/GitHub/phia/phia interior dash/Data feeds needed for search top 1000 domains of search alternatives clicked - Sheet1.csv"
OUTPUT_FILE_PATH = "updated_domains.csv"
BATCH_SIZE = 50
REQUESTS_PER_SECOND = 2
DEVICE = "MOBILE"


def clean_domain(domain):
    """Clean and validate domain format."""
    if not domain or domain.strip() == "":
        return None

    domain = domain.strip()

    # Remove protocol if present
    if domain.startswith(("http://", "https://")):
        domain = urlparse(domain).netloc

    # Remove www. prefix
    if domain.startswith("www."):
        domain = domain[4:]

    # Basic validation
    if "." not in domain or len(domain) < 3:
        return None

    return domain


def call_linkbuilder_api(domain):
    """Call the link builder API for a domain."""
    url = f"https://{domain}"

    payload = {"url": url, "device": DEVICE}

    headers = {"Content-Type": "application/json", "x-phia-token": X_PHIA_TOKEN}

    try:
        response = requests.post(
            PHIA_INTERNAL_API_ENDPOINT, headers=headers, json=payload, timeout=30
        )

        if response.status_code == 200:
            return response.json()
        else:
            print(f"API error for {domain}: {response.status_code}")
            return None

    except Exception as e:
        print(f"Request failed for {domain}: {str(e)}")
        return None


def find_used_network(api_response):
    """Find the network that was actually used (the green highlighted one)."""
    try:
        data = api_response.get("data", {})
        merchant_url = data.get("merchantUrl", {})
        available_merchants = data.get("availableMerchants", [])

        if not merchant_url or not available_merchants:
            return None

        used_merchant_id = merchant_url.get("merchantId")
        if not used_merchant_id:
            return None

        # Find the merchant that matches the used merchant ID
        for merchant in available_merchants:
            if merchant.get("advertiserId") == used_merchant_id:
                return merchant.get("networkName", "").strip()

        return None

    except Exception as e:
        print(f"Error parsing API response: {str(e)}")
        return None


def process_csv():
    """Main function to process the CSV file."""
    print(f"Starting CSV processing...")
    print(f"Input file: {CSV_FILE_PATH}")
    print(f"Output file: {OUTPUT_FILE_PATH}")
    print(f"Batch size: {BATCH_SIZE}")
    print(f"Rate limit: {REQUESTS_PER_SECOND} requests/second")
    print("-" * 50)

    # Read the CSV
    rows = []
    with open(CSV_FILE_PATH, "r", encoding="utf-8") as file:
        reader = csv.DictReader(file)
        rows = list(reader)

    print(f"Loaded {len(rows)} rows from CSV")

    # Find rows that need network updates
    rows_to_update = []
    for i, row in enumerate(rows):
        domain = clean_domain(row.get("domain", ""))
        network = row.get("network", "").strip()

        if domain and not network:
            rows_to_update.append((i, domain))

    print(f"Found {len(rows_to_update)} domains that need network updates")

    if not rows_to_update:
        print("No domains need updating. Exiting.")
        return

    # Process in batches
    updated_count = 0
    failed_count = 0

    for batch_start in range(0, len(rows_to_update), BATCH_SIZE):
        batch_end = min(batch_start + BATCH_SIZE, len(rows_to_update))
        batch = rows_to_update[batch_start:batch_end]

        print(
            f"\nProcessing batch {batch_start//BATCH_SIZE + 1}: domains {batch_start + 1}-{batch_end}"
        )

        for row_index, domain in batch:
            print(f"  Processing: {domain}")

            # Call API
            api_response = call_linkbuilder_api(domain)

            if api_response and api_response.get("success"):
                network_name = find_used_network(api_response)

                if network_name:
                    rows[row_index]["network"] = network_name
                    updated_count += 1
                    print(f"    ✓ Updated: {network_name}")
                else:
                    failed_count += 1
                    print(f"    ✗ No network found")
            else:
                failed_count += 1
                print(f"    ✗ API call failed")

            # Rate limiting
            time.sleep(1.0 / REQUESTS_PER_SECOND)

        print(f"Batch complete. Updated: {updated_count}, Failed: {failed_count}")

    # Write updated CSV
    fieldnames = rows[0].keys() if rows else []
    with open(OUTPUT_FILE_PATH, "w", newline="", encoding="utf-8") as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(rows)

    print(f"\n" + "=" * 50)
    print(f"Processing complete!")
    print(f"Total domains processed: {len(rows_to_update)}")
    print(f"Successfully updated: {updated_count}")
    print(f"Failed: {failed_count}")
    print(f"Success rate: {updated_count/len(rows_to_update)*100:.1f}%")
    print(f"Updated CSV saved to: {OUTPUT_FILE_PATH}")


if __name__ == "__main__":
    # Validate configuration
    if (
        PHIA_INTERNAL_API_ENDPOINT == "YOUR_PHIA_ENDPOINT_HERE"
        or X_PHIA_TOKEN == "YOUR_TOKEN_HERE"
    ):
        print(
            "Error: Please update the PHIA_INTERNAL_API_ENDPOINT and X_PHIA_TOKEN variables"
        )
        exit(1)

    process_csv()
